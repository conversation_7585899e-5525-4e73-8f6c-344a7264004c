import { ChangeDetectionStrategy, Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';

import { BackgroundGradientComponent } from '@/app/core/layout/background-gradient/background-gradient.component';
import { BackgroundGraphicComponent } from '@/app/core/layout/background-graphic/background-graphic.component';
import { HeaderComponent } from '@/app/core/layout/header/header.component';
import { EnvironmentRibbonComponent } from '@/app/shared/atoms/environment-ribbon/environment-ribbon.component';

@Component({
  selector: 'fish-homepage-layout',
  imports: [BackgroundGradientComponent, BackgroundGraphicComponent, HeaderComponent, RouterOutlet, EnvironmentRibbonComponent],
  templateUrl: './homepage-layout.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HomepageLayoutComponent {}
