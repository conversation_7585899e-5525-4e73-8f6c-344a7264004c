/**
 * Utility types for creating type-safe nested property paths
 */

/**
 * Creates a union of all possible nested property paths for an object type.
 * Supports up to 3 levels of nesting (e.g., "a.b.c").
 * 
 * @example
 * type User = { name: string; address: { street: string; city: string } };
 * type UserPaths = NestedPropertyPath<User>; 
 * // Result: "name" | "address" | "address.street" | "address.city"
 */
export type NestedPropertyPath<T> = T extends object
  ? {
      [K in keyof T]: K extends string
        ? T[K] extends object
          ? T[K] extends any[]
            ? K // Arrays are treated as terminal properties
            : K | `${K}.${NestedPropertyPath<T[K]>}`
          : K
        : never;
    }[keyof T]
  : never;

/**
 * Gets the type of a nested property using a dot-notation path.
 * 
 * @example
 * type User = { name: string; address: { street: string } };
 * type StreetType = NestedPropertyType<User, "address.street">; // string
 */
export type NestedPropertyType<T, P extends string> = P extends keyof T
  ? T[P]
  : P extends `${infer K}.${infer Rest}`
  ? K extends keyof T
    ? T[K] extends object
      ? NestedPropertyType<T[K], Rest>
      : never
    : never
  : never;

/**
 * Safely gets a nested property value using dot notation.
 * Returns the value if found, otherwise returns the fallback value.
 * 
 * @param obj - The object to get the property from
 * @param path - The dot-notation path to the property
 * @param fallback - The fallback value if property is not found
 * @returns The property value or fallback
 */
export function getNestedProperty<T extends object, P extends NestedPropertyPath<T>>(
  obj: T,
  path: P,
  fallback: string = 'Keine Angabe'
): string {
  const keys = (path as string).split('.');
  let current: any = obj;

  for (const key of keys) {
    if (current == null || typeof current !== 'object') {
      return fallback;
    }
    current = current[key];
  }

  // Convert the final value to string, handling various types
  if (current == null) {
    return fallback;
  }

  if (typeof current === 'string') {
    return current;
  }

  if (typeof current === 'number' || typeof current === 'boolean') {
    return String(current);
  }

  if (typeof current === 'object') {
    // If it's still an object, try to stringify it or return fallback
    try {
      return JSON.stringify(current);
    } catch {
      return fallback;
    }
  }

  return String(current);
}
