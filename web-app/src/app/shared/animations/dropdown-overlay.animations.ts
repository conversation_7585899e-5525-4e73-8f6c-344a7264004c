import { animate, style, transition, trigger } from '@angular/animations';

export const dropdownOverlayAnimation = trigger('dropdownOverlayAnimation', [
  transition(':enter', [
    style({
      opacity: 0,
      transform: 'translateY(-10px)',
    }),
    animate(
      '240ms ease-out',
      style({
        opacity: 1,
        transform: 'translateY(0)',
      })
    ),
  ]),
  transition(':leave', [
    animate(
      '240ms ease-in',
      style({
        opacity: 0,
        transform: 'translateY(-10px)',
      })
    ),
  ]),
]);
