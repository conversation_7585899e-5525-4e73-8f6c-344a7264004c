<div class="h-full rounded-lg border-2 border-border-divider">
  <div class="grid-flow-row divide-y-2 divide-border-divider rounded-t-lg">
    <div class="flex justify-center bg-background-glass-primary px-7 pb-7 pt-2">
      <img
        [src]="imageSrc"
        alt="license-card"
        style="
          transform: perspective(10em) rotate3D(1.5, 0, -1, 10deg) scale(0.8);
          filter: drop-shadow(0px 5px 11px rgba(0, 0, 0, 0.1)) drop-shadow(0px 19px 19px rgba(0, 0, 0, 0.09))
            drop-shadow(0px 43px 26px rgba(0, 0, 0, 0.05)) drop-shadow(0px 77px 31px rgba(0, 0, 0, 0.01)) drop-shadow(0px 120px 34px rgba(0, 0, 0, 0));
        "
      />
    </div>
    <div class="flex flex-col rounded-b-lg bg-background px-8 pb-8 pt-6">
      <span class="self-stretch text-center text-base font-thin">{{ title }}</span>
      <div class="flex-col gap-4 self-stretch pt-4 text-s">
        <div class="space-y-2" [innerHTML]="text"></div>
      </div>
    </div>
  </div>
</div>
