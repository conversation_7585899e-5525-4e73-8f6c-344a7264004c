import { ChangeDetectionStrategy, Component, Input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'fish-license-cardbox',
  imports: [TranslateModule],
  templateUrl: './license-cardbox.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LicenseCardboxComponent {
  @Input() public imageSrc: string = '';

  @Input() public title: string = '';

  @Input() public text: string = '';
}
