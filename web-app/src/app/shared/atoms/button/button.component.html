<fish-focus-ring [class]="focusRingClasses">
  <button #buttonElement [class]="buttonClasses" [disabled]="disabled" (click)="onButtonClick()" [routerLink]="routeTo()">
    @if (loading) {
      <fish-icon-loader icon class="animate-spin"></fish-icon-loader>
      <span class="px-2">{{ 'common.loading' | translate }}</span>
    } @else {
      <ng-content select="[icon]"></ng-content>
      <span class="whitespace-nowrap px-2 empty:px-0">
        <ng-content />
      </span>
    }
  </button>
</fish-focus-ring>
