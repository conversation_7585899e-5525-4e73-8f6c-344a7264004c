import { ChangeDetectionStrategy, Component, ElementRef, computed, input, output, viewChild } from '@angular/core';
import { RouterLink } from '@angular/router';

import { twMerge } from 'tailwind-merge';

import { FocusRingComponent } from '@/app/shared/atoms/focus-ring/focus-ring.component';
import { headerToggleButtonComponentStyles } from '@/app/shared/atoms/header-toggle-button/header-toggle-button.component.styles';

@Component({
  selector: 'fish-header-toggle-button',
  imports: [FocusRingComponent, RouterLink],
  templateUrl: './header-toggle-button.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HeaderToggleButtonComponent {
  // Inputs
  public readonly isActive = input<boolean>(false);

  public readonly routeTo = input<string | unknown[] | undefined>(undefined);

  public readonly ariaLabel = input<string>();

  public readonly clicked = output<void>();

  public readonly focused = output<FocusEvent>();

  public readonly blurred = output<void>();

  /**
   * Reference to the underlying HTML button element.
   * This allows parent components to access the button element directly
   * without using querySelector.
   */
  public readonly nativeButtonElement = viewChild.required<ElementRef<HTMLButtonElement>>('buttonElement');

  // Fields
  protected readonly classes = computed(() => twMerge(headerToggleButtonComponentStyles({ isActive: this.isActive() })));
}
