<fish-focus-ring class="h-16 w-16">
  <button
    [class]="classes()"
    [routerLink]="routeTo()"
    (click)="clicked.emit()"
    (focus)="focused.emit($event)"
    (blur)="blurred.emit()"
    [attr.aria-label]="ariaLabel()"
    [attr.aria-expanded]="isActive()"
    [attr.aria-pressed]="isActive()"
    #buttonElement
  >
    <div class="absolute right-[3px] top-[3px] text-xs">
      <ng-content select="[badge]"></ng-content>
    </div>
    <div class="absolute inset-0 flex items-center justify-center">
      <ng-content />
    </div>
  </button>
</fish-focus-ring>
