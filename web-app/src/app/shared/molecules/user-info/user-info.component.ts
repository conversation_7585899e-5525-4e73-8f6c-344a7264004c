import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';

import { TranslateModule } from '@ngx-translate/core';
import { KeycloakService } from 'keycloak-angular';

import { TemplateSubstitutionService } from '@/app/core/services/template-substitution.service';
import { UserRole } from '@/app/core/services/user/user.constants';
import { UserService } from '@/app/core/services/user/user.service';
import { UserDropdownMenuComponent } from '@/app/shared/molecules/user-dropdown-menu/user-dropdown-menu.component';

@Component({
  selector: 'fish-user-info',
  imports: [TranslateModule, CommonModule, UserDropdownMenuComponent],
  providers: [TemplateSubstitutionService],
  templateUrl: './user-info.component.html',
})
export class UserInfoComponent {
  private readonly keycloak = inject(KeycloakService);

  private readonly userService = inject(UserService);

  protected readonly userName = toSignal(this.userService.getDisplayName$());

  protected readonly affiliation = toSignal(
    this.keycloak.isUserInRole(UserRole.Official) ? this.userService.getGovernmentOffice$() : this.userService.getExaminationIssuer$()
  );
}
