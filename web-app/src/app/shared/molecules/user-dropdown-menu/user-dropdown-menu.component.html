<fish-header-toggle-button
  cdkOverlayOrigin
  #overlayOrigin="cdkOverlayOrigin"
  [ariaLabel]="'header.user.menu_button_label' | translate"
  [isActive]="menuIsOpen()"
  [attr.aria-haspopup]="'menu'"
  (clicked)="toggleMenu()"
  (focused)="onToggleButtonFocus($event)"
  (blurred)="onToggleButtonBlur()"
  data-testid="user-dropdown-menu-opener"
>
  <div class="absolute inset-0 flex items-center justify-center">
    <fish-profile-icon />
  </div>
</fish-header-toggle-button>

<ng-template
  cdkConnectedOverlay
  [cdkConnectedOverlayOrigin]="overlayOrigin"
  [cdkConnectedOverlayOpen]="menuIsOpen()"
  [cdkConnectedOverlayPositions]="[
    {
      originX: 'end',
      originY: 'bottom',
      overlayX: 'end',
      overlayY: 'top',
      offsetY: 8,
    },
    {
      originX: 'end',
      originY: 'top',
      overlayX: 'end',
      overlayY: 'bottom',
      offsetY: -8,
    },
  ]"
  [cdkConnectedOverlayHasBackdrop]="true"
  (backdropClick)="closeMenu()"
  cdkConnectedOverlayBackdropClass="bg-transparent"
  [cdkConnectedOverlayScrollStrategy]="scrollStrategy"
  [cdkConnectedOverlayDisposeOnNavigation]="true"
  [cdkConnectedOverlayFlexibleDimensions]="false"
  [cdkConnectedOverlayGrowAfterOpen]="false"
  [cdkConnectedOverlayPush]="false"
>
  <div @dropdownOverlayAnimation role="menu">
    <fish-button type="secondary" size="l" (clicked)="handleLogout()" data-testid="logout-button">
      <fish-icon-signout icon size="48" />
      <span [innerText]="'header.user.logout_button_label' | translate"></span>
    </fish-button>
  </div>
</ng-template>
