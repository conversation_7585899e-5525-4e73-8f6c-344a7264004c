import { CommonModule, registerLocaleData } from '@angular/common';
import * as de from '@angular/common/locales/de';
import { Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';

import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { LoadingOverlayComponent } from '@/app/core/layout/loading-overlay/loading-overlay.component';

@Component({
  selector: 'fish-root',
  imports: [RouterOutlet, TranslateModule, CommonModule, LoadingOverlayComponent],
  templateUrl: './app.component.html',
})
export class AppComponent {
  public title = 'web-app';

  constructor(private readonly translate: TranslateService) {
    this.initializeTranslations();
  }

  private initializeTranslations() {
    this.translate.use('');

    registerLocaleData(de.default);
  }
}
