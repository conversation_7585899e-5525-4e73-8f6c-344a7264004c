import { DecimalPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { BansStatisticsTableData } from '@/app/features/reportings/components/molecules/bans-statistics-table/bans-statistics-table.models';
import { IconOwnStateComponent } from '@/app/shared/icons/own-state/own-state.component';
import { IconPlusComponent } from '@/app/shared/icons/plus/plus.component';

@Component({
  selector: 'fish-bans-statistics-table',
  imports: [DecimalPipe, IconOwnStateComponent, TranslateModule, IconPlusComponent],
  templateUrl: './bans-statistics-table.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BansStatisticsTableComponent {
  // Inputs
  public yearData = input<BansStatisticsTableData | undefined>();

  public previousYearData = input<BansStatisticsTableData | undefined>();
}
