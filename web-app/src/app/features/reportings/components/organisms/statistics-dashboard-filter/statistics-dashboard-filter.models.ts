import { FederalStateAbbreviation } from '@digifischdok/ngx-register-sdk';

export const ALL_OFFICES_FILTER_OPTION = 'Alle';

export const ALL_CERTIFICATE_ISSUERS_FILTER_OPTION = 'Alle';

export enum StatisticsDashboardFilterType {
  ALL = 'Alle',
  LICENSES_AND_TAXES = 'Fischereischene & Abgaben', // these two types are for the use case aggregated
  CERTIFICATIONS = 'Prüfungen',
  INSPECTIONS = 'Kontrollen',
  BANS = 'Sperren',
}

export interface StatisticsDashboardFilterData {
  type: StatisticsDashboardFilterType;
  year: string;
  federalState: FederalStateAbbreviation;
  office: string;
  certificateIssuer: string;
}
