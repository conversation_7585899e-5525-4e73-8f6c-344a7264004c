import { ChangeDetectionStrategy, Component, Signal, computed, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { BansStatistics } from '@digifischdok/ngx-register-sdk';

import { HistoricalBarChartValue } from '@/app/features/reportings/components/atoms/historical-bar-chart/historical-bar-chart.model.models';
import { BansStatisticsTableComponent } from '@/app/features/reportings/components/molecules/bans-statistics-table/bans-statistics-table.component';
import { BansStatisticsTableData } from '@/app/features/reportings/components/molecules/bans-statistics-table/bans-statistics-table.models';
import { StatisticsReportTileComponent } from '@/app/features/reportings/components/molecules/statistics-report-tile/statistics-report-tile.component';
import { Statistic } from '@/app/features/reportings/models/statistic.model';
import { fadeInAnimation } from '@/app/shared/animations/fade.animations';
import { IconLockComponent } from '@/app/shared/icons/lock/lock.component';

@Component({
  selector: 'fish-bans-statistics-report-tile',
  imports: [StatisticsReportTileComponent, TranslateModule, IconLockComponent, BansStatisticsTableComponent],
  templateUrl: './bans-statistics-report-tile.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [fadeInAnimation],
})
export class BansStatisticsReportTileComponent {
  // Inputs
  public statistics = input<Array<BansStatistics>>();

  // Fields
  protected readonly primaryStatistic: Signal<Statistic | undefined> = computed(() => {
    const lastYearStatistics = this.lastYearStatistics();
    if (!lastYearStatistics) return undefined;

    return {
      label: lastYearStatistics.year,
      value: lastYearStatistics.data.issued,
      amountType: 'unit',
    };
  });

  protected readonly secondaryStatistic: Signal<Statistic | undefined> = computed(() => {
    const penultimateYearStatistics = this.penultimateYearStatistics();
    if (!penultimateYearStatistics) return undefined;

    return {
      label: penultimateYearStatistics.year,
      value: penultimateYearStatistics.data.issued,
      amountType: 'unit',
    };
  });

  protected readonly chartData: Signal<HistoricalBarChartValue[]> = computed(() => {
    return [...this.sortedByYearStatistics()].reverse().map((stat) => ({
      year: stat.year,
      value: stat.data.issued,
    }));
  });

  private readonly sortedByYearStatistics: Signal<BansStatistics[]> = computed(() => {
    return [...(this.statistics() ?? [])].sort((a, b) => b.year - a.year);
  });

  // the most recent year of the inputted statistics
  private readonly lastYearStatistics: Signal<BansStatistics | null> = computed(() => {
    const sorted = this.sortedByYearStatistics();
    return sorted[0] ?? null;
  });

  // the second most recent year of the inputted statistics
  private readonly penultimateYearStatistics: Signal<BansStatistics | null> = computed(() => {
    const sorted = this.sortedByYearStatistics();
    return sorted.length > 1 ? sorted[1] : null;
  });

  protected readonly lastYearData: Signal<BansStatisticsTableData | undefined> = computed(() => {
    const lastYearStatistics = this.lastYearStatistics();
    if (!lastYearStatistics) return undefined;

    return {
      year: lastYearStatistics.year,
      issued: lastYearStatistics.data.issued,
      active: lastYearStatistics.data.started - lastYearStatistics.data.expired,
    };
  });

  protected readonly penultimateYearData: Signal<BansStatisticsTableData | undefined> = computed(() => {
    const penultimateYearStatistics = this.penultimateYearStatistics();
    if (!penultimateYearStatistics) return undefined;

    return {
      year: penultimateYearStatistics.year,
      issued: penultimateYearStatistics.data.issued,
      active: penultimateYearStatistics.data.started - penultimateYearStatistics.data.expired,
    };
  });
}
