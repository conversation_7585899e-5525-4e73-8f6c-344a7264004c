import { Meta, StoryObj, argsToTemplate } from '@storybook/angular';

import { BansStatisticsReportTileComponent } from '@/app/features/reportings/components/organisms/bans-statistics-report-tile/bans-statistics-report-tile.component';

const actualYear = new Date().getFullYear();

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<BansStatisticsReportTileComponent> = {
  title: 'statistics/BansStatisticsReportTile',
  component: BansStatisticsReportTileComponent,
  render: (args) => ({
    props: { ...args },
    template: `<div class="flex justify-center">
                  <div class="w-[460px]">
                    <fish-bans-statistics-report-tile ${argsToTemplate(args)}> </fish-bans-statistics-report-tile>
                  </div>
               </div>`,
  }),
  args: {
    statistics: [
      {
        year: actualYear - 5,
        data: { issued: 80, expired: 5, started: 80, active: 0 },
      },
      {
        year: actualYear - 4,
        data: { issued: 250, expired: 20, started: 250, active: 0 },
      },
      {
        year: actualYear - 3,
        data: { issued: 300, expired: 5, started: 300, active: 0 },
      },
      {
        year: actualYear - 2,
        data: { issued: 280, expired: 10, started: 280, active: 0 },
      },
      {
        year: actualYear - 1,
        data: { issued: 150, expired: 20, started: 150, active: 0 },
      },
      {
        year: actualYear,
        data: { issued: 220, expired: 10, started: 220, active: 0 },
      },
    ],
  },
};

export default meta;

type Story = StoryObj<BansStatisticsReportTileComponent>;

export const Default: Story = {};
