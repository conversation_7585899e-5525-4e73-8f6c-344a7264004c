import { AfterViewInit, ChangeDetectionStrategy, Component, inject, signal, viewChild } from '@angular/core';
import { FormBuilder } from '@angular/forms';

import { TranslateModule } from '@ngx-translate/core';

import { DisabilityCertificateVerifiedFormComponent } from '@/app/features/register-edit/components/organisms/disability-certificate-verified-form/disability-certificate-verified-form.component';
import { LimitedLicenseQualificationProofBoxFormGroup } from '@/app/features/register-edit/components/organisms/limited-license-qualification-proof-box/limited-license-qualification-proof-box.models';
import { LimitedLicenseValidityPeriodFormComponent } from '@/app/features/register-edit/components/organisms/limited-license-validity-period-form/limited-license-validity-period-form.component';
import { CardContentComponent } from '@/app/shared/atoms/card-content/card-content.component';
import { CardHeaderComponent } from '@/app/shared/atoms/card-header/card-header.component';
import { FormErrorMessageComponent } from '@/app/shared/atoms/form-error-message/form-error-message.component';
import { FormComponent } from '@/app/shared/atoms/form/form.component';
import { CardComponent } from '@/app/shared/molecules/card/card.component';

@Component({
  selector: 'fish-limited-license-qualification-proof-box',
  imports: [
    CardComponent,
    CardHeaderComponent,
    DisabilityCertificateVerifiedFormComponent,
    LimitedLicenseValidityPeriodFormComponent,
    FormErrorMessageComponent,
    TranslateModule,
    CardContentComponent,
  ],
  templateUrl: './limited-license-qualification-proof-box.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LimitedLicenseQualificationProofBoxComponent
  extends FormComponent<LimitedLicenseQualificationProofBoxFormGroup>
  implements AfterViewInit
{
  // FIELDS
  public override formGroup!: LimitedLicenseQualificationProofBoxFormGroup;

  protected readonly disabilityCertificateVerifiedForm = viewChild.required(DisabilityCertificateVerifiedFormComponent);

  protected readonly limitedLicenseValidityPeriodForm = viewChild.required(LimitedLicenseValidityPeriodFormComponent);

  protected readonly disabilityCertificateVerifiedControlInvalid = signal(false);

  protected readonly limitedLicenseValidityPeriodFormInvalid = signal(false);

  // DEPENDENCIES
  public readonly formBuilder = inject(FormBuilder);

  public ngAfterViewInit(): void {
    this.initFormGroup();
  }

  private initFormGroup() {
    this.formGroup = this.formBuilder.group({
      disabilityCertificateVerified: this.disabilityCertificateVerifiedForm().formGroup.controls.disabilityCertificateVerified,
      limitedLicenseValidityPeriod: this.limitedLicenseValidityPeriodForm().formGroup,
    });

    this.limitedLicenseValidityPeriodForm().formGroup.disable();

    const disabilityCertificateVerifiedControl = this.formGroup.controls.disabilityCertificateVerified;
    disabilityCertificateVerifiedControl.statusChanges.subscribe(() => {
      this.disabilityCertificateVerifiedControlInvalid.set(
        disabilityCertificateVerifiedControl.status === 'INVALID' && disabilityCertificateVerifiedControl.touched
      );
      if (disabilityCertificateVerifiedControl.value) {
        this.limitedLicenseValidityPeriodForm().formGroup.enable();
      } else {
        this.limitedLicenseValidityPeriodForm().formGroup.disable();
        this.limitedLicenseValidityPeriodForm().formGroup.reset();
      }
    });

    const limitedLicenseValidityPeriodForm = this.formGroup.controls.limitedLicenseValidityPeriod;
    limitedLicenseValidityPeriodForm.statusChanges.subscribe(() => {
      this.limitedLicenseValidityPeriodFormInvalid.set(
        limitedLicenseValidityPeriodForm.status === 'INVALID' && limitedLicenseValidityPeriodForm.touched
      );
    });
  }

  public override validate(): void {
    this.formGroup.controls.disabilityCertificateVerified.markAllAsTouched();
    this.formGroup.controls.disabilityCertificateVerified.updateValueAndValidity();
    if (this.formGroup.controls.disabilityCertificateVerified.value) {
      this.formGroup.controls.limitedLicenseValidityPeriod.markAllAsTouched();
      this.formGroup.controls.limitedLicenseValidityPeriod.updateValueAndValidity();
    }
  }
}
