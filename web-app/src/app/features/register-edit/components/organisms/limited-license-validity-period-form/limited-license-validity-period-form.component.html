<div class="flex flex-col gap-4">
  <span class="break-words text-base font-thin" [innerText]="'edit_form.limited_license_qualification_proof.validity_period_form.text' | translate">
  </span>
  @if (isTimeLimitable()) {
    <fish-period-radio-item
      [disabled]="!!disabled()"
      [endDateControl]="formGroup.controls.validTo"
      [endDateErrorMapping$]="validToErrorMapping$"
      [endDateLabel]="'edit_form.limited_license_qualification_proof.validity_period_form.options.limited.input_label' | translate"
      [label]="'edit_form.limited_license_qualification_proof.validity_period_form.options.limited.label' | translate"
      [name]="'ban-period-radio'"
      [radioControl]="formGroup.controls.validityPeriodType"
      [value]="'limited'"
      data-testid="temporary-ban-period-radio-item"
    ></fish-period-radio-item>
  }

  <fish-period-radio-item
    [disabled]="!!disabled()"
    [label]="'edit_form.limited_license_qualification_proof.validity_period_form.options.lifelong.label' | translate"
    [name]="'ban-period-radio'"
    [radioControl]="formGroup.controls.validityPeriodType"
    [value]="'unlimited'"
    data-testid="permanent-ban-period-radio-item"
  ></fish-period-radio-item>
</div>
