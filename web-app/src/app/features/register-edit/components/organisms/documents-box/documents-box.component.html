<fish-card>
  <fish-card-header>
    <fish-icon-document-pdf icon size="64"></fish-icon-document-pdf>
    <div class="text-l">
      {{ 'edit_form.documents.box.title' | translate }}
    </div>
  </fish-card-header>

  <fish-card-content [class]="'h-full overflow-y-auto'">
    <fish-card-section>
      <div class="flex flex-col items-stretch gap-4">
        @for (document of pdfDocuments(); track document.documentId; let index = $index) {
          <fish-document-item
            [document]="document"
            [federalState]="getDocumentFederalState(document)"
            [isFetchingDocument]="documentToFetch()?.documentId === document.documentId"
            (printDocumentButtonClicked)="fetchDocument($event)"
            data-testid="document-results-document-item"
          ></fish-document-item>
        }
      </div>
    </fish-card-section>

    @if (showMailSection()) {
      <fish-card-section>
        <fish-send-documents-item
          (sendDocumentsButtonClicked)="sendDocumentsDialog.open()"
          data-testid="document-results-mail-item"
        ></fish-send-documents-item>
      </fish-card-section>
    }
  </fish-card-content>
</fish-card>

<fish-send-documents-dialog
  #sendDocumentsDialog
  (confirmButtonClicked)="sendDocuments($event)"
  [isLoading]="isSendingDocument()"
></fish-send-documents-dialog>
<fish-send-documents-success-feedback></fish-send-documents-success-feedback>
