import { cva } from 'class-variance-authority';

export const periodRadioItemContainerStyles = cva(
  ['flex w-full flex-row items-center gap-4 rounded-lg py-4 pl-8 pr-6 transition-all duration-240 ease-out'],
  {
    variants: {
      invalid: {
        true: 'text-feedback-text-error border-feedback-text-error border bg-feedback-background-warning-1 shadow-glass-red-tint',
        false: 'shadow-glass-white-tint has-[:checked]:bg-tint-blue has-[:checked]:shadow-glass-blue-tint',
      },
      disabled: {
        true: 'text-action-text-disabled',
        false: '',
      },
    },
    defaultVariants: {
      invalid: false,
      disabled: false,
    },
  }
);
