import { NgClass } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit, computed, input } from '@angular/core';
import { FormControl } from '@angular/forms';

import { Observable, map, merge, startWith } from 'rxjs';
import { twMerge } from 'tailwind-merge';

import { periodRadioItemContainerStyles } from '@/app/features/register-edit/components/molecules/period-radio-item/period-radio-item.styles';
import { FocusRingComponent } from '@/app/shared/atoms/focus-ring/focus-ring.component';
import { RadioComponent } from '@/app/shared/atoms/radio/radio.component';
import { FormFieldComponent } from '@/app/shared/organisms/form-field/form-field.component';
import { ValidationErrorMapping } from '@/app/shared/organisms/form-field/form-field.models';
import { toComputed } from '@/app/shared/utils/rxJsInterop.utils';

@Component({
  selector: 'fish-period-radio-item',
  imports: [FocusRingComponent, FormFieldComponent, RadioComponent, NgClass],
  templateUrl: './period-radio-item.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PeriodRadioItemComponent implements OnInit {
  public readonly label = input.required<string>();

  public readonly name = input.required<string>();

  public readonly value = input.required<string | number>();

  public readonly radioControl = input.required<FormControl<string | number | null>>();

  public readonly startDateControl = input<FormControl<string | null>>();

  public readonly startDateLabel = input<string>();

  public readonly startDateErrorMapping$ = input<Observable<ValidationErrorMapping>>();

  public readonly endDateControl = input<FormControl<string | null>>();

  public readonly endDateLabel = input<string>();

  public readonly endDateErrorMapping$ = input<Observable<ValidationErrorMapping>>();

  public readonly disabled = input<boolean>(false);

  private readonly invalid = toComputed(() => {
    return merge(this.radioControl().statusChanges, this.radioControl().parent?.statusChanges || []).pipe(
      map(() => this.radioControl().invalid && this.radioControl().touched)
    );
  });

  protected readonly containerClasses = computed<string>(() => {
    return twMerge(
      periodRadioItemContainerStyles({
        invalid: this.invalid(),
        disabled: this.disabled(),
      })
    );
  });

  public ngOnInit(): void {
    // Enable or Disable based on the checkbox value
    this.radioControl()
      .valueChanges.pipe(startWith(this.radioControl().value))
      .subscribe((radioValue) => {
        const checked = radioValue === this.value();
        const startDateControl = this.startDateControl();
        const endDateControl = this.endDateControl();

        if (startDateControl) {
          if (checked) {
            startDateControl.enable();
          } else {
            startDateControl.disable();
          }
        }

        // the period radio item might not have an end date
        if (endDateControl) {
          if (checked) {
            endDateControl.enable();
          } else {
            endDateControl.disable();
          }
        }
      });
  }
}
