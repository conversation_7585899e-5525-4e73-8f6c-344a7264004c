<table fishRegisterFileTable>
  <tr fishRegisterFileTableRow>
    @for (column of columns(); track column) {
      <th
        fishRegisterFileTableHeader
        [innerText]="'register_file.labels.filed_process.filed_process_data.' + category() + '.' + column | translate"
      ></th>
    }
  </tr>
  @for (dataEntry of data(); track $index) {
    <tr fishRegisterFileTableRow>
      @for (column of columns(); track column) {
        <td fishRegisterFileTableCell [innerText]="getPropertyValue(dataEntry, column)"></td>
      }
    </tr>
  }
</table>
