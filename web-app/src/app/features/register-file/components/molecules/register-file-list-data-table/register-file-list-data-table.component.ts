import { ChangeDetectionStrategy, Component, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { RegisterFileTableCellDirective } from '@/app/features/register-file/directives/register-file-table-cell.directive';
import { RegisterFileTableHeaderDirective } from '@/app/features/register-file/directives/register-file-table-header.directive';
import { RegisterFileTableRowDirective } from '@/app/features/register-file/directives/register-file-table-row.component';
import { RegisterFileTableDirective } from '@/app/features/register-file/directives/register-file-table.directive';
import { NestedPropertyPath, getNestedProperty } from '@/app/shared/types/nested-property-path.types';

@Component({
  selector: 'fish-register-file-list-data-table',
  imports: [
    RegisterFileTableDirective,
    RegisterFileTableRowDirective,
    RegisterFileTableHeaderDirective,
    RegisterFileTableCellDirective,
    TranslateModule,
  ],
  templateUrl: './register-file-list-data-table.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RegisterFileListDataTableComponent<T extends readonly object[]> {
  public readonly data = input.required<T>();

  public readonly category = input.required<string>();

  public readonly columns = input.required<NestedPropertyPath<T[0]>[]>();

  protected getPropertyValue(dataEntry: T[0], column: NestedPropertyPath<T[0]>): string {
    return getNestedProperty(dataEntry, column);
  }
}
