<table fishRegisterFileTable>
  <tr fishRegisterFileTableRow>
    <th fishRegisterFileTableHeader [innerText]="'Feld'"></th>
    <th fishRegisterFileTableHeader [innerText]="'Wert'"></th>
  </tr>
  @for (row of rows(); track row) {
    <tr fishRegisterFileTableRow>
      <td fishRegisterFileTableCell [innerText]="'register_file.filed_process.filed_process_data.' + category() + '.' + row | translate"></td>
      <td fishRegisterFileTableCell [innerText]="getPropertyValue(data(), row)"></td>
    </tr>
  }
</table>
