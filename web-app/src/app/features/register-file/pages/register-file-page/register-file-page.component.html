<div class="mx-auto max-w-[800px] pt-12 print:pt-0">
  <div class="flex items-center justify-between">
    <fish-logo size="m" />
    <h1 [innerText]="'Registerauszug'" class="text-l font-bold text-action-primary"></h1>
  </div>
  <table class="mb-20 ml-auto mt-12 text-xs">
    <tr>
      <th class="text-left">Register-ID:</th>
      <td class="text-right" [innerText]="registerFile()?.registerEntryId"></td>
    </tr>
    <tr>
      <th class="text-left">Auszug erstellt am:</th>
      <td class="text-right" [innerText]="registerFile()?.createdAt | date: 'dd.MM.yyyy, HH:mm:ss \'Uhr\''"></td>
    </tr>
    <tr>
      <th class="text-left">Auszug erstellt von:</th>
      <td class="text-right" [innerText]="registerFile()?.createdByInstitution"></td>
    </tr>
  </table>
  <div class="flex flex-col gap-10">
    @for (process of registerFile()?.filedProcesses; track process.processTimestamp) {
      <div>
        <h2
          [innerText]="'Vorgang: ' + ('register_file.filed_process.process_type.' + process.processType.toLowerCase() | translate)"
          class="mb-4 mt-2 text-base text-action-primary"
        ></h2>
        <h3 [innerText]="'Vorgangskopfdaten'" class="mb-2 mt-6 font-bold"></h3>
        <fish-register-file-data-table
          [category]="'head'"
          [data]="process"
          [rows]="['actingInstitution', 'federalStateOfInstitution', 'processTimestamp', 'federalState', 'issuedBy']"
        ></fish-register-file-data-table>
        @if (process.filedProcessData.person) {
          <h3 [innerText]="'Personendaten'" class="mb-2 mt-6 font-bold"></h3>
          <fish-register-file-data-table
            [data]="process.filedProcessData.person"
            [category]="'person'"
            [rows]="['title', 'firstname', 'lastname', 'birthdate', 'birthplace', 'nationality', 'email']"
          ></fish-register-file-data-table>
        }
        @if (process.filedProcessData.person?.address) {
          <h3 [innerText]="'Adresse'" class="mb-2 mt-6 font-bold"></h3>
          <fish-register-file-data-table
            [category]="'address'"
            [data]="process.filedProcessData.person!.address!"
            [rows]="['street', 'streetNumber', 'postcode', 'city', 'detail']"
          ></fish-register-file-data-table>
        }
        @if (process.filedProcessData.person?.officeAddress) {
          <h3 [innerText]="'Behördenadresse'" class="mb-2 mt-6 font-bold"></h3>
          <fish-register-file-data-table
            [category]="'officeAddress'"
            [data]="process.filedProcessData.person!.officeAddress!"
            [rows]="['office', 'deliverTo', 'street', 'streetNumber', 'postcode', 'city', 'detail']"
          ></fish-register-file-data-table>
        }
        @if (process.filedProcessData.qualificationsProof) {
          <h3 [innerText]="'Nachweise'" class="mb-2 mt-6 font-bold"></h3>
          <fish-register-file-data-table
            [category]="'qualificationsProof'"
            [data]="process.filedProcessData.qualificationsProof"
            [rows]="['type', 'fishingCertificateId', 'otherFormOfProofId', 'federalState', 'passedOn', 'issuedBy']"
          ></fish-register-file-data-table>
        }
        @if (process.filedProcessData.fees && process.filedProcessData.fees.length > 0) {
          <h3 [innerText]="'Gebühren'" class="mb-2 mt-6 font-bold"></h3>
          <fish-register-file-list-data-table
            [category]="'fees'"
            [data]="process.filedProcessData.fees"
            [columns]="['federalState', 'validFrom', 'validTo', 'paymentInfo.amount', 'paymentInfo.type']"
          ></fish-register-file-list-data-table>
        }
        @if (process.filedProcessData.taxes && process.filedProcessData.taxes.length > 0) {
          <h3 [innerText]="'Fischereiabgaben'" class="mb-2 mt-6 font-bold"></h3>
          <fish-register-file-list-data-table
            [category]="'taxes'"
            [data]="process.filedProcessData.taxes"
            [columns]="['taxId', 'federalState', 'validFrom', 'validTo', 'paymentInfo']"
          ></fish-register-file-list-data-table>
        }
        @if (process.filedProcessData.fishingLicense) {
          <h3 [innerText]="'Fischereischein'" class="mb-2 mt-6 font-bold"></h3>
          <fish-register-file-data-table
            [category]="'fishingLicense'"
            [data]="process.filedProcessData.fishingLicense"
            [rows]="['type', 'number', 'issuingFederalState']"
          ></fish-register-file-data-table>
        }
        @if (process.filedProcessData.fishingLicense?.validityPeriods && process.filedProcessData.fishingLicense!.validityPeriods.length > 0) {
          <h3 [innerText]="'Gültigkeitszeiten'" class="mb-2 mt-6 font-bold"></h3>
          <fish-register-file-list-data-table
            [category]="'validityPeriods'"
            [data]="process.filedProcessData.fishingLicense!.validityPeriods"
            [columns]="['validFrom', 'validTo']"
          ></fish-register-file-list-data-table>
        }
        @if (process.filedProcessData.ban) {
          <h3 [innerText]="'Sperre'" class="mb-2 mt-6 font-bold"></h3>
          <fish-register-file-data-table
            [category]="'ban'"
            [data]="process.filedProcessData.ban"
            [rows]="['fileNumber', 'reportedBy', 'at', 'from', 'to']"
          ></fish-register-file-data-table>
        }
        @if (process.filedProcessData.consentInfo) {
          <h3 [innerText]="'Zustimmungen'" class="mb-2 mt-6 font-bold"></h3>
          <fish-register-file-data-table
            [category]="'consentInfo'"
            [data]="process.filedProcessData.consentInfo"
            [rows]="['gdprAccepted', 'submittedByThirdParty', 'selfDisclosureAccepted', 'proofOfMoveVerified', 'disabilityCertificateVerified']"
          ></fish-register-file-data-table>
        }
        @if (process.filedProcessData.identificationDocuments && process.filedProcessData.identificationDocuments.length > 0) {
          <h3 [innerText]="'Identifikationsdokumente'" class="mb-2 mt-6 font-bold"></h3>
          <fish-register-file-list-data-table
            [category]="'identificationDocuments'"
            [data]="process.filedProcessData.identificationDocuments"
            [columns]="['fishingTaxId', 'fishingLicenseId', 'type', 'validFrom', 'validTo', 'issuedDate']"
          ></fish-register-file-list-data-table>
        }
      </div>
    }
  </div>
</div>
