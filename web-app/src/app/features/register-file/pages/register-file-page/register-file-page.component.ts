import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { TranslateModule } from '@ngx-translate/core';
import { catchError, map } from 'rxjs';

import { RegisterFileResponse, RegisterFileService } from '@digifischdok/ngx-register-sdk';

import { RouteParamService } from '@/app/core/services/route-param.service';
import { RegisterFileDataTableComponent } from '@/app/features/register-file/components/molecules/register-file-data-table/register-file-data-table.component';
import { RegisterFileListDataTableComponent } from '@/app/features/register-file/components/molecules/register-file-list-data-table/register-file-list-data-table.component';
import { LogoComponent } from '@/app/shared/atoms/logo/logo.component';
import { ServerDialogService } from '@/app/shared/services/server-dialog.service';

@Component({
  selector: 'fish-register-file-page',
  imports: [DatePipe, TranslateModule, LogoComponent, RegisterFileListDataTableComponent, RegisterFileDataTableComponent],
  templateUrl: './register-file-page.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RegisterFilePageComponent {
  private readonly registerEntryId = inject(RouteParamService).getParamOrFail('registerEntryId', inject(ActivatedRoute));

  protected readonly registerFile = signal<RegisterFileResponse | null>(null);

  private readonly registerFileService = inject(RegisterFileService);

  private readonly serverErrorDialogService = inject(ServerDialogService);

  constructor() {
    this.registerFileService
      .registerFileControllerGet(this.registerEntryId, 'response')
      .pipe(
        map((response) => response.body),
        catchError((error: unknown) => {
          this.serverErrorDialogService.handleServerError(error);
          return [];
        })
      )
      .subscribe((registerFile) => {
        this.registerFile.set(registerFile);
      });
  }
}
