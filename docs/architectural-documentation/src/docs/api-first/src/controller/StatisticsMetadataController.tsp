import "@typespec/http";
import "@typespec/openapi3";

import "../common.tsp";
import "../Fischereiregister.tsp";
import "../model/enum/FederalStateAbbreviation.tsp";

using TypeSpec.Http;

namespace Fischereiregister;

@tag("Statistics Metadata")
@route("/statistics/metadata")
interface StatisticsMetadataController {

    @get
    @route("/offices")
    @doc("Retrieves all offices present in the in the Taxes and Licenses Statistics")
    getOffices(
        @doc("List of years for which the offices are requested. If no year is provided, all offices of all years found in system are returned.")
        @query({
            format: "multi",
            name: "year",
        }) year?: int32[],

        @doc("The federal state for which the list of offices should be retrieved. If no federal state is given, an overall general list of all available offices is to be delivered")
        @query federalState?: FederalStateAbbreviation,

    ): WithStandardErrors<string[]>;

    @get
    @route("/certification-issuers")
    @doc("Retrieves all Certification Issuers present in the in the Certification Statistics")
    getCertificationIssuers(
        @doc("List of years for which the Certification Issuers are requested. If no year is provided, all Certification Issuers of all years found in system are returned.")
        @query({
            format: "multi",
            name: "year",
        }) year?: int32[],

        @doc("The federal state for which the list of Certification Issuers should be retrieved. If no federal state is given, an overall general list of all available Certification Issuers is to be delivered")
        @query federalState?: FederalStateAbbreviation,

    ): WithStandardErrors<string[]>;

    @get
    @route("/years")
    @doc("Retrieves all years for which statistics are available")
    getYears(
    ): WithStandardErrors<int32[]>;

}
