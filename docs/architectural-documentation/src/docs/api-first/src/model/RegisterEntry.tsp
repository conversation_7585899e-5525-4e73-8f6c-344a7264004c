import "@typespec/http";
import "@typespec/rest";
import "@typespec/versioning";
import "@typespec/openapi3";
import "./Jurisdiction.tsp";
import "./QualificationsProof.tsp";
import "./Ban.tsp";
import "./Tax.tsp";
import "./Fee.tsp";
import "./IdentificationDocument.tsp";
import "./Person.tsp";
import "./LimitedLicenseApplication.tsp";

model RegisterEntry {
    registerId: string;

    @doc("Contains information on the person and address of the register entry.")
    person: Person;

    @doc("Who has jurisdiction over the register entry")
    jurisdiction: Jurisdiction;

    @doc("Contains information of the currently active ban, if any")
    ban?: Ban;

    @doc("List of Proofs (certificates or documents) that the person is qualified to perform fishing activities")
    qualificationsProofs: QualificationsProof[];

    @doc("Licenses of the register entry, can contain 0-3 items, depending on the license type.")
    fishingLicenses: FishingLicense[];

    @doc("All fishing taxes that the person registered has paid so far.")
    taxes: Tax[];

    @doc("All fees that the person registered has paid so far.")
    fees: Fee[];

    @doc("The latest consent info, that was submitted.")
    consentInfo?: ConsentInfo;

    @doc("Representations of the analog (currently valid) identification documents connected to the register (pdfs / check-cards)")
    identificationDocuments: IdentificationDocument[];

    @doc("When the person has applied for a limited license, this field contains the application data. Once an application is converted to a full limited license, the application data will be deleted.")
    limitedLicenseApplication?: LimitedLicenseApplication;

    @doc("id of the service account in the online service portal, if the user has any")
    serviceAccountId:string;

    @doc("inbox Reference of the service account in the online service portal, if the user has any")
    inboxReference:string;
}
