@doc("Bans statistics for a specific year.")
model BansStatistics {
    @doc("The year for which bans statistics are aggregated.")
    year: int32;

    @doc("Breakdown of the bans data")
    data: {
        @doc("The amount of bans issued")
        issued: int32;

        @doc("The amount of bans that started taking effect in the given year")
        started: int32;
        
        @doc("The amount of bans that expired in the given year")
        expired: int32,

        @doc("The aggregated amount of bans that are active in the given year")
        active: int32,
    };
}
