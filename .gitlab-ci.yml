stages:
  - build
  - test
  - pre-analysis
  - analysis
  - deploy

# General rules
variables:
  RULES_CHANGES_PATH: '**/*'
  E2E_TEST_STAGE: "dev"
  # Deployment
  CI_STAGE_BRANCH: "stage"
  CI_TEST_BRANCH: "test"
  CI_PROD_BRANCH: "main"

.base-rules:
  rules:
    - &not_triggered_by_schedule
      if: '$CI_PIPELINE_SOURCE == "schedule"'
      when: never
    - &not_triggered_by_push_except_develop
      if: '$CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH != "develop"'
      when: never
    - &not_triggered_by_tag_creation
      if: $CI_COMMIT_TAG
      when: never
    - &triggered_by_merge_request_with_changes
      if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      changes:
        - $RULES_CHANGES_PATH
    - &triggered_by_push_on_develop
      if: '$CI_COMMIT_BRANCH == "develop"'
      changes:
        - '**/*'
    - &triggered_by_manual
      when: manual
      allow_failure: true

.base-rules-branch-only:
  rules:
    - *not_triggered_by_schedule
    - if: '$CI_COMMIT_BRANCH == "develop"'
      changes:
        - $RULES_CHANGES_PATH

.base-rules-dependency-scanning:
  rules:
    - if: '$CI_pipeline_SOURCE == "schedule"'
      when: always
    - *not_triggered_by_push_except_develop
    - *not_triggered_by_tag_creation
    - *triggered_by_merge_request_with_changes
    - *triggered_by_manual

.base-rules-environment-branches:
  rules:
    - *not_triggered_by_schedule
    - if: '$CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH && $CI_COMMIT_BRANCH != $CI_STAGE_BRANCH && $CI_COMMIT_BRANCH != $CI_TEST_BRANCH && $CI_COMMIT_BRANCH != $CI_PROD_BRANCH'
      when: never
    - *not_triggered_by_tag_creation
    - *triggered_by_merge_request_with_changes
    - if: $CI_COMMIT_BRANCH == $CI_STAGE_BRANCH
        || $CI_COMMIT_BRANCH == $CI_TEST_BRANCH
        || $CI_COMMIT_BRANCH == $CI_PROD_BRANCH
        || $CI_COMMIT_BRANCH == "main"
        || $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - $RULES_CHANGES_PATH
    - *triggered_by_manual

# Project-specific rules
.web:
  image: node:18-alpine
  extends: .base-rules
  before_script:
    - cd web-app
    - npm config set //${ARTIFACTORY_INT_REGISTRY_NPM}/:_authToken=${ARTIFACTORY_INT_TOKEN}
    - npm ci --ignore-scripts
    - npm install -g @angular/cli
    - "export SUB_PROJECT_VERSION=$(sed -n 's/.*\"version\": \"\\(.*\\)\",/\\1/p' ${CI_PROJECT_DIR}/${SUB_PROJECT_PATH}/package.json)"
  variables:
    RULES_CHANGES_PATH: 'web-app/**/*'
    SUB_PROJECT_PATH: "web-app"

.register-service:
  extends: .base-rules
  image: gradle:8.12-jdk21-alpine
  before_script:
    - cd backend/register-service
    - "export SUB_PROJECT_VERSION=$(sed -n \"/version =/s/.*'\\(.*\\)'.*/\\1/p\" ${CI_PROJECT_DIR}/${SUB_PROJECT_PATH}/build.gradle)"
  variables:
    SUB_PROJECT_PATH: "backend/register-service"

.mobile-app:
  extends: .base-rules
  before_script:
    - cd mobile-app/apps/app_mobile
    - "export SUB_PROJECT_VERSION=$(grep -o 'version:\\ [0-9\\.\\+]*' ${CI_PROJECT_DIR}/${SUB_PROJECT_PATH}/pubspec.yaml | awk '{print $2}')"
  variables:
    SUB_PROJECT_PATH: "mobile-app/apps/app_mobile"

# Jobs
web-build:
  stage: build
  extends: .web
  tags:
    - medium
  script:
    - ng build --configuration "production"
    - ng lint --output-file lint-report.json --format json
  artifacts:
    when: always
    paths:
      - web-app/lint-report.json

web-generate-bom:
  stage: build
  extends: .web
  tags:
    - extra-small
  rules: !reference [ .base-rules-branch-only, rules ]
  script:
    - npm run sbom
    - mv ${CI_PROJECT_DIR}/web-app/application.cdx.json ${CI_PROJECT_DIR}/web-app/sbom-${SUB_PROJECT_VERSION}.cdx.json
    - md5sum ${CI_PROJECT_DIR}/web-app/sbom-${SUB_PROJECT_VERSION}.cdx.json
    - apk add --no-cache curl && curl -u $ARTIFACTORY_INT_USER:$ARTIFACTORY_INT_TOKEN -T ${CI_PROJECT_DIR}/web-app/sbom-${SUB_PROJECT_VERSION}.cdx.json "https://$ARTIFACTORY_INT_REGISTRY/artifactory/fischereiregister-npm/%40digifischdok/web-app/-/%40digifischdok/web-app-sbom-${SUB_PROJECT_VERSION}.cdx.json"
  artifacts:
    reports:
      cyclonedx:
        - ${CI_PROJECT_DIR}/web-app/sbom-*.cdx.json
  variables:
    RULES_CHANGES_PATH: '**/*'
    SUB_PROJECT_PATH: "web-app"

register-service-build:
  stage: build
  rules: !reference [ .base-rules-environment-branches, rules ]
  extends: .register-service
  tags:
    - medium
  variables:
    GRADLE_USER_HOME: '${CI_PROJECT_DIR}/.gradle'
  script:
    - gradle build -x test
  artifacts:
    paths:
      - ${CI_PROJECT_DIR}/.gradle/caches/modules-2/files-2.1/**/**/*.jar
      - backend/**/build/classes/java/main

register-service-generate-bom:
  stage: build
  extends: .register-service
  tags:
    - extra-small
  rules: !reference [ .base-rules-branch-only, rules ]
  script:
    - gradle cyclonedxBom
    - mv ${CI_PROJECT_DIR}/backend/register-service/build/reports/application.cdx.json ${CI_PROJECT_DIR}/backend/register-service/sbom-${SUB_PROJECT_VERSION}.cdx.json
    - apk add --no-cache curl && curl -u $ARTIFACTORY_INT_USER:$ARTIFACTORY_INT_TOKEN -T ${CI_PROJECT_DIR}/backend/register-service/sbom-${SUB_PROJECT_VERSION}.cdx.json "https://$ARTIFACTORY_INT_REGISTRY/artifactory/fischereiregister-maven-release/de/adesso/fischereiregister/register-service/${SUB_PROJECT_VERSION}/register-service-sbom-${SUB_PROJECT_VERSION}.cdx.json"
  artifacts:
    reports:
      cyclonedx:
        - ${CI_PROJECT_DIR}/backend/register-service/sbom-*.cdx.json

mobile-app-generate-bom:
  stage: build
  extends: .mobile-app
  tags:
    - extra-small
  image:
    name: aquasec/trivy:0.58.1
    entrypoint: [ '' ]
  rules: !reference [ .base-rules-branch-only, rules ]
  script:
    - trivy fs --format cyclonedx --output application.cdx.json .
    - mv application.cdx.json ${CI_PROJECT_DIR}/mobile-app/apps/app_mobile/sbom-${SUB_PROJECT_VERSION}.cdx.json
    - apk add --no-cache curl && curl -u $ARTIFACTORY_INT_USER:$ARTIFACTORY_INT_TOKEN -T ${CI_PROJECT_DIR}/mobile-app/apps/app_mobile/sbom-${SUB_PROJECT_VERSION}.cdx.json "https://$ARTIFACTORY_INT_REGISTRY/artifactory/fischereiregister-generic/mobile-app/${SUB_PROJECT_VERSION}/mobile-app-sbom-${SUB_PROJECT_VERSION}.cdx.json"
  artifacts:
    reports:
      cyclonedx:
        - ${CI_PROJECT_DIR}/mobile-app/apps/app_mobile/sbom-*.cdx.json

register-service-unit-test:
  stage: test
  extends: .register-service
  rules: !reference [ .base-rules-environment-branches, rules ]
  tags:
    - medium
  dependencies: [ ]
  script:
    - gradle unitTest jacocoFullReport --continue
  artifacts:
    reports:
      junit: backend/register-service/**/build/test-results/unitTest/**/TEST-*.xml
      coverage_report:
        coverage_format: jacoco
        path: backend/register-service/build/reports/jacoco/jacocoFullReport/jacocoFullReport.xml
    paths:
      - backend/register-service/build/reports/jacoco/jacocoFullReport/jacocoFullReport.xml

register-service-integration-test:
  stage: test
  extends: .register-service
  tags:
    - medium
  dependencies: [ ]
  script:
    - gradle integrationTest --continue --info
  artifacts:
    reports:
      junit: backend/register-service/build/test-results/integrationTest/**/TEST-*.xml
  when: manual

e2e-test:
  stage: test
  image: cypress/browsers:node-18.20.3-chrome-125.0.6422.141-1-ff-126.0.1-edge-125.0.2535.85-1
  tags:
    - large
  script:
    - cd web-app
    - npm config set //${ARTIFACTORY_INT_REGISTRY_NPM}/:_authToken=${ARTIFACTORY_INT_TOKEN}
    - npm ci --ignore-scripts
    - npx cypress install --force
    - npm install --save-dev allure-cypress mochawesome mochawesome-report-generator
    - |
      if [ "$E2E_TEST_STAGE" == "dev" ]; then
        CYPRESS_BASE_URL=$DEV_STAGE_URL npx cypress run --env envName=dev || true
      elif [ "$E2E_TEST_STAGE" == "test" ]; then
        CYPRESS_BASE_URL=$TEST_STAGE_URL npx cypress run --env envName=test || true
      else
        echo "Unbekannte Stage: $E2E_TEST_STAGE"
        exit 1
      fi
    - npx mochawesome-report-generator "${CI_PROJECT_DIR}/web-app/cypress/results/mochawesome/$E2E_TEST_STAGE/*.json" --reportDir "${CI_PROJECT_DIR}/web-app/cypress/results/mochawesome/$E2E_TEST_STAGE" || true
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule"'
      when: always
    - when: manual
      allow_failure: true
  artifacts:
    when: always
    paths:
      - ${CI_PROJECT_DIR}/web-app/cypress/results/mochawesome/$E2E_TEST_STAGE
      - ${CI_PROJECT_DIR}/web-app/cypress/results/videos/**/*.mp4
      - ${CI_PROJECT_DIR}/web-app/cypress/results/screenshots/**/*.png
    expire_in: 1 week

run-sonarqube-analysis:
  stage: analysis
  extends: .base-rules-environment-branches
  image:
    name: sonarsource/sonar-scanner-cli:11.1
    entrypoint: [ '' ]
  variables:
    SONAR_USER_HOME: '${CI_PROJECT_DIR}/.sonar'
    GIT_DEPTH: '0'
  cache:
    key: '${CI_JOB_NAME}'
    paths:
      - .sonar/cache
  needs:
    - register-service-build
    - register-service-unit-test
  script:
    - sonar-scanner
      -Dsonar.projectKey=Fischereiregister-1
      -Dsonar.java.libraries=${CI_PROJECT_DIR}/.gradle/caches/modules-2/files-2.1/**/**/*.jar
      -Dsonar.qualitygate.wait=true
      -Dsonar.qualitygate.timeout=1000
      -Dsonar.java.binaries=backend/**/build/classes/java/main
      -Dsonar.eslint.reportPaths=web-app/lint-report.json
      -Dsonar.exclusions=backend/**/src/test/java/**/*,backend/register-service/migrations/**/*,mobile-app/**/*,web-app/cypress/**,
      -Dsonar.test.inclusions=backend/**/src/test/java/**/*.java
      -Dsonar.coverage.exclusions=**/*.ts,**/*.js,**/*.html,**/*Dto.java,backend/**/import_testdata/**/*,backend/*/src/test/**,backend/**/observability/**/*,**/*Controller.java,docs/**,backend/register-service/migrations/**/*,backend/**/domain/**/*,backend/**/security/model/**/*,**/*Command.java,**/*Event.java,**/*Error.java,**/*Exception.java,**/*Request.java,**/*Config.java,**/*Configuration.java,**/order/OrderServiceImpl.java,**/order/OrderProperties.java,**/*Converter.java,**/*Repository.java,**/*View.java,**/*Properties.java,**/enums/**/*.java,**/type/**/*.java,**/data/**/*,**/model/**/*
      -Dsonar.coverage.jacoco.xmlReportPaths=backend/register-service/build/reports/jacoco/jacocoFullReport/jacocoFullReport.xml
      -Dsonar.cpd.exclusions=web-app/src/app/shared/icons/**,**/*Test.java,web-app/cypress/**
      -Dsonar.issue.ignore.multicriteria=e1,e2
      -Dsonar.issue.ignore.multicriteria.e1.ruleKey=java:S112
      -Dsonar.issue.ignore.multicriteria.e1.resourceKey=**/*
      -Dsonar.issue.ignore.multicriteria.e2.ruleKey=java:S1452
      -Dsonar.issue.ignore.multicriteria.e2.resourceKey=**/*Controller.java
  allow_failure: true

.trivy_base:
  stage: analysis
  image:
    name: aquasec/trivy:0.59.0
    entrypoint: [ "" ]
  extends: .base-rules-dependency-scanning
  artifacts:
    reports:
      container_scanning: gl-dependency-scan.json
    paths:
      - ${CI_PROJECT_DIR}/gl-dependency-scan.json
    expire_in: 1 week
  cache:
    paths:
      - .trivycache/

trivy_scan_web_app:
  extends: .trivy_base
  script:
    - trivy fs --scanners vuln
      --skip-dirs "node_modules"
      --format template
      --template "@/contrib/gitlab.tpl"
      -o gl-dependency-scan.json
      ${CI_PROJECT_DIR}/web-app
  variables:
    RULES_CHANGES_PATH: 'web-app/package.json'

register_service_generate_lockfile:
  stage: pre-analysis
  extends: .register-service
  tags:
    - extra-small
  rules: !reference [ .base-rules-dependency-scanning, rules ]
  script:
    - gradle dependencies --write-locks
  artifacts:
    paths:
      - ${CI_PROJECT_DIR}/backend/register-service/gradle.lockfile
  variables:
    RULES_CHANGES_PATH: 'backend/register-service/build.gradle'

trivy_scan_register_service:
  extends: .trivy_base
  dependencies:
    - register_service_generate_lockfile
  script:
    - trivy fs --scanners vuln
      --skip-dirs "target"
      --format template
      --template "@/contrib/gitlab.tpl"
      -o gl-dependency-scan.json
      ${CI_PROJECT_DIR}/backend/register-service
  variables:
    RULES_CHANGES_PATH: 'backend/register-service/build.gradle'

deploy:
  stage: deploy
  tags:
    - extra-small
  rules:
    - *not_triggered_by_schedule
    - if: $CI_COMMIT_BRANCH == $CI_STAGE_BRANCH
        || $CI_COMMIT_BRANCH == $CI_TEST_BRANCH
        || $CI_COMMIT_BRANCH == $CI_PROD_BRANCH
        || $CI_COMMIT_BRANCH == "main"
        || $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - $RULES_CHANGES_PATH
  dependencies: [ ]
  image: bitnami/git:latest
  script:
    - git fetch --unshallow
    - git config user.name "${GIT_USER_ID}"
    - |
      if git remote | grep -q "dataport_origin"; then
      git remote rm dataport_origin
      fi
    - git remote add dataport_origin https://oauth2:$GIT_ACCESS_TOKEN@$GIT_URL
    - git push -f dataport_origin HEAD:refs/heads/${CI_COMMIT_BRANCH}
  variables:
    RULES_CHANGES_PATH: "**/*"

pages:
  stage: deploy
  extends: .base-rules-branch-only
  dependencies: [ ]
  image: node:lts
  script:
    - mkdir -p ${CI_PROJECT_DIR}/public
    - cp -r gitlab-pages/* ${CI_PROJECT_DIR}/public
    - cd web-app
    - npm config set //${ARTIFACTORY_INT_REGISTRY_NPM}/:_authToken=${ARTIFACTORY_INT_TOKEN}
    - npm ci
    - npm run build-storybook
    - mkdir -p ${CI_PROJECT_DIR}/public/storybook
    - cp -r storybook-static/* ${CI_PROJECT_DIR}/public/storybook
    - cd ../docs/architectural-documentation/src/docs/api-first
    - npm ci
    - npm run build
    - mkdir -p ${CI_PROJECT_DIR}/public/api
    - cp -r build/* ${CI_PROJECT_DIR}/public/api
  artifacts:
    paths:
      - public

# API-First
register-service-build-spec:
  stage: build
  extends: .base-rules-branch-only
  image: node:18-alpine
  script:
    - npm install -g @typespec/compiler
    - cd docs/architectural-documentation/src/docs/api-first
    - npm ci
    - npm run compile
  artifacts:
    paths:
      - ${CI_PROJECT_DIR}/docs/architectural-documentation/src/docs/api-first/tsp-output/@typespec/openapi3/openapi.Fischereiregister.*.yaml
    expire_in: 1 day
  variables:
    RULES_CHANGES_PATH: "docs/architectural-documentation/src/docs/api-first/**/*"

register-service-build-sdk-angular:
  stage: build
  extends: .base-rules-branch-only
  needs: [ register-service-build-spec ]
  image: openapitools/openapi-generator-cli:latest
  script:
    - cd docs/architectural-documentation/src/docs/api-first
    - "export SUB_PROJECT_VERSION=$(ls ${CI_PROJECT_DIR}/docs/architectural-documentation/src/docs/api-first/tsp-output/@typespec/openapi3/openapi.Fischereiregister.*.yaml | sed -E 's|^.*/openapi\\.Fischereiregister\\.([0-9]+\\.[0-9]+\\.[0-9]+(-snapshot)?)\\.yaml$|\\1|')"
    - mkdir -p ${CI_PROJECT_DIR}/openapi-angular
    - /usr/local/bin/docker-entrypoint.sh generate
      -g typescript-angular
      -i ${CI_PROJECT_DIR}/docs/architectural-documentation/src/docs/api-first/tsp-output/@typespec/openapi3/openapi.Fischereiregister.${SUB_PROJECT_VERSION}.yaml
      -o ${CI_PROJECT_DIR}/openapi-angular
  artifacts:
    paths:
      - ${CI_PROJECT_DIR}/openapi-angular
    expire_in: 1 day
  variables:
    RULES_CHANGES_PATH: "docs/architectural-documentation/src/docs/api-first/**/*"

register-service-publish-sdk-angular:
  stage: deploy
  extends: .base-rules-branch-only
  needs:
    - register-service-build-spec
    - register-service-build-sdk-angular
  image: node:18-alpine
  script:
    - npm install -g @angular/cli
    - ng new library-workspace --no-create-application --skip-git
    - cd library-workspace
    - ng generate library @digifischdok/ngx-register-sdk
    - cd projects/digifischdok/ngx-register-sdk
    - "export SUB_PROJECT_VERSION=$(ls ${CI_PROJECT_DIR}/docs/architectural-documentation/src/docs/api-first/tsp-output/@typespec/openapi3/openapi.Fischereiregister.*.yaml | sed -E 's|^.*/openapi\\.Fischereiregister\\.([0-9]+\\.[0-9]+\\.[0-9]+)(-snapshot)?\\.yaml$|\\1|')"
    - npm version ${SUB_PROJECT_VERSION}
    - cp -r ${CI_PROJECT_DIR}/openapi-angular/* src/lib
    - echo "export * from './lib/index';" > src/public-api.ts
    - cd ../../../
    - ng build @digifischdok/ngx-register-sdk
    - cd dist/digifischdok/ngx-register-sdk
    - npm config set //${ARTIFACTORY_INT_REGISTRY_NPM}/:_authToken=${ARTIFACTORY_INT_TOKEN}
    - npm publish --registry https://${ARTIFACTORY_INT_REGISTRY_NPM_FISH}
  variables:
    RULES_CHANGES_PATH: "docs/architectural-documentation/src/docs/api-first/**/*"

register-service-build-sdk-spring:
  stage: build
  extends: .base-rules-branch-only
  needs: [ register-service-build-spec ]
  image: openapitools/openapi-generator-cli:latest
  script:
    - cd docs/architectural-documentation/src/docs/api-first
    - "export SUB_PROJECT_VERSION=$(ls ${CI_PROJECT_DIR}/docs/architectural-documentation/src/docs/api-first/tsp-output/@typespec/openapi3/openapi.Fischereiregister.*.yaml | sed -E 's|^.*/openapi\\.Fischereiregister\\.([0-9]+\\.[0-9]+\\.[0-9]+(-snapshot)?)\\.yaml$|\\1|')"
    - mkdir -p ${CI_PROJECT_DIR}/openapi-spring
    - /usr/local/bin/docker-entrypoint.sh generate
      -g spring
      -i ${CI_PROJECT_DIR}/docs/architectural-documentation/src/docs/api-first/tsp-output/@typespec/openapi3/openapi.Fischereiregister.${SUB_PROJECT_VERSION}.yaml
      -o ${CI_PROJECT_DIR}/openapi-spring
      -c spring.config.yaml
      -t templates/spring
  artifacts:
    paths:
      - ${CI_PROJECT_DIR}/openapi-spring
    expire_in: 1 day
  variables:
    RULES_CHANGES_PATH: "docs/architectural-documentation/src/docs/api-first/**/*"

register-service-publish-sdk-spring:
  stage: deploy
  extends: .base-rules-branch-only
  needs:
    - register-service-build-spec
    - register-service-build-sdk-spring
  image: maven:3.9.9-eclipse-temurin-17
  script:
    - cd ${CI_PROJECT_DIR}/openapi-spring
    - cat ${ARTIFACTORY_MAVEN_SETTINGS} > settings.xml
    - sed -i '/<\/project>/e cat ${ARTIFACTORY_MAVEN_POM}' pom.xml
    - cat pom.xml
    - mvn deploy -s settings.xml -f pom.xml -X
  variables:
    RULES_CHANGES_PATH: "docs/architectural-documentation/src/docs/api-first/**/*"