pluginManagement {
    repositories {
        maven {
            url 'https://common-gradle-gradle-remote.repo-in.zcdi.dataport.de/artifactory/common-gradle-gradle-remote'
            credentials {
                username = System.getenv("GRADLE_REGISTRY_USER_ID")
                password = System.getenv("GRADLE_REGISTRY_TOKEN")
            }
        }
        gradlePluginPortal()
    }
}
rootProject.name = 'register-service'
include 'core'
include 'common'
include 'testutils'
include 'views'
include 'views:register_data'
findProject(':views:register_data')?.name = 'register_data'
include 'views:search'
findProject(':views:search')?.name = 'search'
include 'views:ban_expiration'
findProject(':views:ban_expiration')?.name = 'ban_expiration'
include 'views:licenses_statistics'
findProject(':views:licenses_statistics')?.name = 'licenses_statistics'
include 'views:register_entry_search'
findProject(':views:register_entry_search')?.name = 'register_entry_search'
include 'views:taxes_statistics'
findProject(':views:taxes_statistics')?.name = 'taxes_statistics'
include 'views:fees_statistics'
findProject(':views:fees_statistics')?.name = 'fees_statistics'
include 'views:bans_statistics'
findProject(':views:bans_statistics')?.name = 'bans_statistics'
include 'views:certifications_statistics'
findProject(':views:certifications_statistics')?.name = 'certifications_statistics'
include 'views:filed_process'
findProject(':views:filed_process')?.name = 'filed_process'
include 'message-service'
include 'card_orders'
include 'migrations'
include 'inspector-protocol'

