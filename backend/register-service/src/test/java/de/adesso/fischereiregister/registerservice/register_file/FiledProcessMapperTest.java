package de.adesso.fischereiregister.registerservice.register_file;

import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import de.adesso.fischereiregister.registerservice.register_file.mapper.FiledProcessMapper;
import de.adesso.fischereiregister.view.filed_process.persistence.FiledProcessData;
import de.adesso.fischereiregister.view.filed_process.persistence.FiledProcessType;
import de.adesso.fischereiregister.view.filed_process.persistence.FiledProcessView;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;
import org.openapitools.model.FiledProcess;

import java.time.Instant;

import static org.junit.jupiter.api.Assertions.assertNotNull;


public class FiledProcessMapperTest {

    private final FiledProcessMapper mapper = Mappers.getMapper(FiledProcessMapper.class);

    @DisplayName("Test Mapping from domain to api model.")
    @Test
    void testProcessToApiModel() {
        RegisterEntry entry = DomainTestData.createRegisterEntry();
        // ARRANGE
        FiledProcessData processData = FiledProcessData.builder()
                .fishingLicense(entry.getFishingLicenses().getFirst())
                .consentInfo(DomainTestData.createLimitedLicenseConsentInfo())
                .identificationDocuments(entry.getIdentificationDocuments())
                .build();

        FiledProcessView filedProcessView = new FiledProcessView(
                0L,
                DomainTestData.registerId,
                "Tolle Fische",
                FederalState.SH,
                Instant.now(),
                FederalState.SH,
                null,
                FiledProcessType.FISHING_LICENSE_CREATED,
                processData);

        // ACT
        FiledProcess result = mapper.toDto(filedProcessView);

        // ASSERT
        assertNotNull(result);
        assertNotNull(result.getActingInstitution());
        assertNotNull(result.getFiledProcessData());

        org.openapitools.model.FiledProcessData payload = result.getFiledProcessData();
        assertNotNull(payload.getConsentInfo());


    }
}
