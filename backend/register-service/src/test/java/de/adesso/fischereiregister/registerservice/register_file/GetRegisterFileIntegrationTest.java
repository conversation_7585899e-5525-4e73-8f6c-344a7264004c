package de.adesso.fischereiregister.registerservice.register_file;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.hasSize;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext()
public class GetRegisterFileIntegrationTest {

    @Autowired
    private MockMvc mvc;

    @Test
    @DisplayName("""
				GET /api/register-file/{registerEntryId}
				Verify that the register-file returns a register file for the given registerEntryId and check if the data matches,
				only with processes form the matching jurisdiction.
			""")
    void getRegisterEntryTest() throws Exception {

        ResultActions result =  mvc.perform(MockMvcRequestBuilders.get("http://localhost:8080/register-file/e4b6af13-1feb-4a4d-9c46-76298a0611cf")
                .accept(MediaType.APPLICATION_JSON));

        result.andExpect(status().isOk());
        result.andExpect(jsonPath("$.registerEntryId", containsString("e4b6af13-1feb-4a4d-9c46-76298a0611cf")));
        result.andExpect(jsonPath("$.filedProcesses", hasSize(3)));
    }
}
