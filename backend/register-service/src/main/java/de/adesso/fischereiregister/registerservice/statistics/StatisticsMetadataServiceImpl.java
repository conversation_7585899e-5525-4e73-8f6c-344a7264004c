package de.adesso.fischereiregister.registerservice.statistics;

import de.adesso.fischereiregister.protocol.service.InspectorProtocolService;
import de.adesso.fischereiregister.view.bans_statistics.services.BansStatisticsViewService;
import de.adesso.fischereiregister.view.certifications_statistics.services.CertificationsStatisticsViewService;
import de.adesso.fischereiregister.view.fees_statistics.services.FeesStatisticsViewService;
import de.adesso.fischereiregister.view.licenses_statistics.services.LicensesStatisticsViewService;
import de.adesso.fischereiregister.view.taxes_statistics.services.TaxesStatisticsViewService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * Implementation of StatisticsMetadataService that aggregates metadata from multiple statistics sources.
 */
@Service
@AllArgsConstructor
@Slf4j
public class StatisticsMetadataServiceImpl implements StatisticsMetadataService {

    private final CertificationsStatisticsViewService certificationsStatisticsViewService;
    private final TaxesStatisticsViewService taxesStatisticsViewService;
    private final LicensesStatisticsViewService licensesStatisticsViewService;
    private final BansStatisticsViewService bansStatisticsViewService;
    private final FeesStatisticsViewService feesStatisticsViewService;
    private final InspectorProtocolService inspectorProtocolService;

    @Override
    public List<String> getAvailableCertificationIssuers() {
        return certificationsStatisticsViewService.getAvailableIssuers();
    }

    @Override
    public List<String> getAvailableCertificationIssuersByYears(List<Integer> years) {
        return certificationsStatisticsViewService.getAvailableIssuersByYears(years);
    }

    @Override
    public List<String> getAvailableCertificationIssuersByFederalState(String federalState) {
        return certificationsStatisticsViewService.getAvailableIssuersByFederalState(federalState);
    }

    @Override
    public List<String> getAvailableCertificationIssuersByYearsAndFederalState(List<Integer> years, String federalState) {
        return certificationsStatisticsViewService.getAvailableIssuersByYearsAndFederalState(years, federalState);
    }

    @Override
    public List<String> getAvailableOffices() {
        log.debug("Aggregating offices from taxes and licenses statistics");
        
        List<String> taxOffices = taxesStatisticsViewService.getAvailableOffices();
        List<String> licenseOffices = licensesStatisticsViewService.getAvailableOffices();
        
        return aggregateAndSortOffices(taxOffices, licenseOffices);
    }

    @Override
    public List<String> getAvailableOfficesByYears(List<Integer> years) {
        log.debug("Aggregating offices from taxes and licenses statistics for years: {}", years);
        
        List<String> taxOffices = taxesStatisticsViewService.getAvailableOfficesByYears(years);
        List<String> licenseOffices = licensesStatisticsViewService.getAvailableOfficesByYears(years);
        
        return aggregateAndSortOffices(taxOffices, licenseOffices);
    }

    @Override
    public List<String> getAvailableOfficesByFederalState(String federalState) {
        log.debug("Aggregating offices from taxes and licenses statistics for federal state: {}", federalState);
        
        List<String> taxOffices = taxesStatisticsViewService.getAvailableOfficesByFederalState(federalState);
        List<String> licenseOffices = licensesStatisticsViewService.getAvailableOfficesByFederalState(federalState);
        
        return aggregateAndSortOffices(taxOffices, licenseOffices);
    }

    @Override
    public List<String> getAvailableOfficesByYearsAndFederalState(List<Integer> years, String federalState) {
        log.debug("Aggregating offices from taxes and licenses statistics for years: {} and federal state: {}", years, federalState);
        
        List<String> taxOffices = taxesStatisticsViewService.getAvailableOfficesByYearsAndFederalState(years, federalState);
        List<String> licenseOffices = licensesStatisticsViewService.getAvailableOfficesByYearsAndFederalState(years, federalState);
        
        return aggregateAndSortOffices(taxOffices, licenseOffices);
    }

    @Override
    public List<Integer> getAvailableYears() {
        log.debug("Aggregating years from all statistics sources");

        List<Integer> certificationYears = certificationsStatisticsViewService.getAvailableYears();
        List<Integer> taxYears = taxesStatisticsViewService.getAvailableYears();
        List<Integer> licenseYears = licensesStatisticsViewService.getAvailableYears();
        List<Integer> banYears = bansStatisticsViewService.getAvailableYears();
        List<Integer> feeYears = feesStatisticsViewService.getAvailableYears();
        List<Integer> inspectionYears = inspectorProtocolService.getAvailableYears();

        return aggregateAndSortYears(certificationYears, taxYears, licenseYears, banYears, feeYears, inspectionYears);
    }

    /**
     * Aggregates offices from multiple sources, removes duplicates, and sorts them.
     *
     * @param taxOffices     Offices from taxes statistics
     * @param licenseOffices Offices from licenses statistics
     * @return A sorted list of distinct office names
     */
    private List<String> aggregateAndSortOffices(List<String> taxOffices, List<String> licenseOffices) {
        return Stream.concat(taxOffices.stream(), licenseOffices.stream())
                .filter(Objects::nonNull) // in case of corrupt data
                .distinct()
                .sorted()
                .toList();
    }

    /**
     * Aggregates years from multiple statistics sources, removes duplicates, and sorts them in descending order.
     *
     * @param yearLists Variable number of year lists from different statistics sources
     * @return A sorted list of distinct years in descending order
     */
    private List<Integer> aggregateAndSortYears(List<Integer>... yearLists) {
        return Stream.of(yearLists)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .filter(Objects::nonNull) // in case of corrupt data
                .distinct()
                .sorted(Comparator.reverseOrder()) // Descending order (newest first)
                .toList();
    }
}
