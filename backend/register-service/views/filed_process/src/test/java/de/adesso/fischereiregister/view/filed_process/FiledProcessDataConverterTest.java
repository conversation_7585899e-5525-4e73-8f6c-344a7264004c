package de.adesso.fischereiregister.view.filed_process;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import de.adesso.fischereiregister.view.filed_process.converter.FiledProcessDataConverter;
import de.adesso.fischereiregister.view.filed_process.persistence.FiledProcessData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
class FiledProcessDataConverterTest {

    private FiledProcessDataConverter converter;

    @BeforeEach
    void setUp() {
        ObjectMapper mapper = new ObjectMapper();
        converter = new FiledProcessDataConverter(mapper);
    }

    @Test
    void testConvertToDatabaseColumn() {
        // ARRANGE
        FiledProcessData data = FiledProcessData.builder()
                .person(DomainTestData.createPerson())
                .qualificationsProof(DomainTestData.createQualificationsProof())
                .consentInfo(DomainTestData.createJurisdictionConsentInfo())
                .build();

        // ACT
        String json = converter.convertToDatabaseColumn(data);

        // ASSERT
        assertNotNull(json);
        assertTrue(json.contains("person"));
        assertTrue(json.contains("qualificationsProof"));
        assertTrue(json.contains("consentInfo"));
    }

    @Test
    void testConvertToEntityAttribute() {
        // ARRANGE
        String json = "{\"person\":{\"title\":\"DR.\",\"firstname\":\"Max\",\"lastname\":\"Mustermann\",\"birthname\":\"Musterwar\",\"birthplace\":\"Berlin\",\"birthdate\":\"20.05.1990\",\"address\":null,\"officeAddress\":null,\"nationality\":\"deutsch\",\"email\":null},\"qualificationsProof\":{\"type\":\"CERTIFICATE\",\"fishingCertificateId\":\"4711\",\"otherFormOfProofId\":null,\"federalState\":\"SH\",\"examinerId\":null,\"passedOn\":\"2024-06-12\",\"issuedBy\":\"Fischfreunde Övelgönne e.V.\"},\"taxes\":null,\"fees\":null,\"ban\":null,\"consentInfo\":{\"type\":\"JurisdictionConsentInfo\",\"gdprAccepted\":true,\"submittedByThirdParty\":true,\"selfDisclosureAccepted\":true,\"proofOfMoveVerified\":true},\"identificationDocuments\":null,\"fishingLicense\":null,\"fishingLicenseNumber\":null}";

        // ACT
        FiledProcessData data = converter.convertToEntityAttribute(json);

        // ASSERT
        assertNotNull(data);
        assertNotNull(data.getPerson());
        assertNotNull(data.getQualificationsProof());
        assertNotNull(data.getConsentInfo());
    }
}
