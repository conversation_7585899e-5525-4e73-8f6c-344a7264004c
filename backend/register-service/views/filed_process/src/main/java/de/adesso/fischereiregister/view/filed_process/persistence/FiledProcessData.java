package de.adesso.fischereiregister.view.filed_process.persistence;

import de.adesso.fischereiregister.core.model.Ban;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.AbstractConsentInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;


@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
public class FiledProcessData {
    Person person;
    String serviceAccountId;
    QualificationsProof qualificationsProof;
    List<Tax> taxes;
    List<Fee> fees;
    Ban ban;
    AbstractConsentInfo consentInfo;
    List<IdentificationDocument> identificationDocuments;
    FishingLicense fishingLicense;
    String fishingLicenseNumber;
}
