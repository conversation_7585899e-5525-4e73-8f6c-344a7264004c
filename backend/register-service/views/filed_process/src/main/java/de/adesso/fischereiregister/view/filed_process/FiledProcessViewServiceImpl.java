package de.adesso.fischereiregister.view.filed_process;

import de.adesso.fischereiregister.core.model.Address;
import de.adesso.fischereiregister.core.model.Ban;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.consent.JurisdictionConsentInfo;
import de.adesso.fischereiregister.core.model.consent.TaxConsentInfo;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.view.filed_process.persistence.FiledProcessData;
import de.adesso.fischereiregister.view.filed_process.persistence.FiledProcessType;
import de.adesso.fischereiregister.view.filed_process.persistence.FiledProcessView;
import de.adesso.fischereiregister.view.filed_process.persistence.FiledProcessViewRepository;
import jakarta.persistence.EntityNotFoundException;
import lombok.AllArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
@AllArgsConstructor
public class FiledProcessViewServiceImpl implements FiledProcessViewService {
    private final FiledProcessViewRepository filedProcessViewRepository;
    private final JdbcTemplate jdbcTemplate;

    @Override
    @Transactional
    public void truncateView() {
        jdbcTemplate.execute("TRUNCATE TABLE filed_process_view");
        filedProcessViewRepository.deleteAll();
    }

    @Override
    @Transactional
    public List<FiledProcessView> getFiledProcesses(UUID registerEntryId, FederalState federalState) {
        List<FiledProcessView> processesForRegisterEntry = filedProcessViewRepository.findByRegisterEntryId(registerEntryId);
        if(processesForRegisterEntry.isEmpty()) {
            throw new EntityNotFoundException();
        }

        return filedProcessViewRepository.findByRegisterEntryIdAndFederalStateOfInstitution(registerEntryId, federalState);
    }

    @Override
    public void createQualificationsProofCreatedProcess(ProcessHeaderData processHeaderData,
                                                        Person person,
                                                        QualificationsProof qualificationsProof) {
        FiledProcessView process = this.createViewWithHeaderData(processHeaderData);
        process.setFiledProcessType(FiledProcessType.QUALIFICATIONS_PROOF_CREATED);

        FiledProcessData processData = FiledProcessData.builder()
                .person(person)
                .qualificationsProof(qualificationsProof)
                .build();

        process.setFiledProcessData(processData);

        this.filedProcessViewRepository.save(process);
    }

    @Override
    public void createJurisdictionMovedProcess(ProcessHeaderData processHeaderData,
                                               List<Tax> taxes,
                                               JurisdictionConsentInfo consentInfo,
                                               List<IdentificationDocument> identificationDocuments,
                                               Jurisdiction jurisdiction) {
        FiledProcessView process = this.createViewWithHeaderData(processHeaderData);
        process.setFiledProcessType(FiledProcessType.JURISDICTION_CHANGED);

        FiledProcessData processData = FiledProcessData.builder()
                .taxes(taxes)
                .consentInfo(consentInfo)
                .identificationDocuments(identificationDocuments)
                .build();

        process.setFiledProcessData(processData);

        this.filedProcessViewRepository.save(process);
    }

    @Override
    public void createFishingLicenseCreatedProcess(ProcessHeaderData processHeaderData,
                                                   Person person,
                                                   String serviceAccountId,
                                                   List<Tax> taxes,
                                                   List<Fee> fees,
                                                   List<IdentificationDocument> identificationDocuments,
                                                   FishingLicense fishingLicense,
                                                   Address officesAddress) {
        FiledProcessView process = this.createViewWithHeaderData(processHeaderData);
        process.setFiledProcessType(FiledProcessType.FISHING_LICENSE_CREATED);

        FiledProcessData processData = FiledProcessData.builder()
                .person(person)
                .serviceAccountId(serviceAccountId)
                .taxes(taxes)
                .fees(fees)
                .identificationDocuments(identificationDocuments)
                .fishingLicense(fishingLicense)
                .build();

        process.setFiledProcessData(processData);

        this.filedProcessViewRepository.save(process);
    }

    @Override
    public void createFishingLicenseExtendedProcess(ProcessHeaderData processHeaderData,
                                                    Person person,
                                                    String serviceAccountId,
                                                    List<Tax> taxes,
                                                    List<Fee> fees,
                                                    List<IdentificationDocument> identificationDocuments,
                                                    String fishingLicenseNumber,
                                                    Address officesAddress) {
        FiledProcessView process = this.createViewWithHeaderData(processHeaderData);
        process.setFiledProcessType(FiledProcessType.FISHING_LICENSE_EXTENDED);

        FiledProcessData processData = FiledProcessData.builder()
                .person(person)
                .serviceAccountId(serviceAccountId)
                .taxes(taxes)
                .fees(fees)
                .identificationDocuments(identificationDocuments)
                .fishingLicenseNumber(fishingLicenseNumber)
                .build();

        process.setFiledProcessData(processData);

        this.filedProcessViewRepository.save(process);
    }

    @Override
    public void createReplacementCardOrderedProcess(ProcessHeaderData processHeaderData,
                                                    Person person,
                                                    String serviceAccountId,
                                                    List<Tax> taxes,
                                                    List<Fee> fees,
                                                    List<IdentificationDocument> identificationDocuments,
                                                    FishingLicense fishingLicense,
                                                    Address address) {
        FiledProcessView process = this.createViewWithHeaderData(processHeaderData);
        process.setFiledProcessType(FiledProcessType.REPLACEMENT_CARD_ORDERED);

        FiledProcessData processData = FiledProcessData.builder()
                .person(person)
                .serviceAccountId(serviceAccountId)
                .taxes(taxes)
                .fees(fees)
                .identificationDocuments(identificationDocuments)
                .fishingLicense(fishingLicense)
                .build();

        process.setFiledProcessData(processData);

        this.filedProcessViewRepository.save(process);
    }

    @Override
    public void createFishingTaxPayedProcess(ProcessHeaderData processHeaderData,
                                             Person person,
                                             String serviceAccountId,
                                             List<Tax> taxes,
                                             TaxConsentInfo consentInfo,
                                             List<IdentificationDocument> identificationDocuments) {
        FiledProcessView process = this.createViewWithHeaderData(processHeaderData);
        process.setFiledProcessType(FiledProcessType.FISHING_TAX_CREATED);

        FiledProcessData processData = FiledProcessData.builder()
                .person(person)
                .serviceAccountId(serviceAccountId)
                .taxes(taxes)
                .consentInfo(consentInfo)
                .identificationDocuments(identificationDocuments)
                .build();

        process.setFiledProcessData(processData);

        this.filedProcessViewRepository.save(process);
    }

    @Override
    public void createBannedProcess(ProcessHeaderData processHeaderData, Ban ban) {
        FiledProcessView process = this.createViewWithHeaderData(processHeaderData);
        process.setFiledProcessType(FiledProcessType.BANNED);

        FiledProcessData processData = FiledProcessData.builder()
                .ban(ban)
                .build();

        process.setFiledProcessData(processData);

        this.filedProcessViewRepository.save(process);
    }

    @Override
    public void createUnbannedProcess(ProcessHeaderData processHeaderData) {
        FiledProcessView process = this.createViewWithHeaderData(processHeaderData);
        process.setFiledProcessType(FiledProcessType.UNBANNED);

        FiledProcessData processData = FiledProcessData.builder()
                .build();

        process.setFiledProcessData(processData);

        this.filedProcessViewRepository.save(process);
    }

    @Override
    public void createLimitedLicenseApplicationProcess(ProcessHeaderData processHeaderData,
                                                       Person person,
                                                       String serviceAccountId,
                                                       List<Fee> fees,
                                                       ConsentInfo consentInfo) {
        FiledProcessView process = this.createViewWithHeaderData(processHeaderData);
        process.setFiledProcessType(FiledProcessType.LIMITED_LICENSE_APPLICATION_CREATED);

        FiledProcessData processData = FiledProcessData.builder()
                .person(person)
                .serviceAccountId(serviceAccountId)
                .fees(fees)
                .consentInfo(consentInfo)
                .build();

        process.setFiledProcessData(processData);

        this.filedProcessViewRepository.save(process);
    }

    @Override
    public void createLimitedLicenseRejectedProcess(ProcessHeaderData processHeaderData) {
        FiledProcessView process = this.createViewWithHeaderData(processHeaderData);
        process.setFiledProcessType(FiledProcessType.LIMITED_LICENSE_APPLICATION_REJECTED);

        FiledProcessData processData = FiledProcessData.builder()
                .build();

        process.setFiledProcessData(processData);

        this.filedProcessViewRepository.save(process);
    }

    private FiledProcessView createViewWithHeaderData(ProcessHeaderData processHeaderData) {
        FiledProcessView process = new FiledProcessView();
        process.setRegisterEntryId(processHeaderData.getRegisterEntryId());
        process.setActingInstitution(processHeaderData.getActingInstitution());
        process.setFederalStateOfInstitution(processHeaderData.getFederalStateOfInstitution());
        process.setProcessTimestamp(processHeaderData.getTimestamp());
        process.setFederalState(processHeaderData.getFederalState());
        process.setIssuedBy(processHeaderData.getIssuedBy());
        return process;
    }
}
